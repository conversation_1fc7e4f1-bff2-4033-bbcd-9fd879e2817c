using Orleans;
using Orleans.Streams;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Curio.Infrastructure.Configuration;
using Curio.Shared.Users;
using Orleans.Runtime;
using Curio.Orleans.Interfaces.Infrastructure;

namespace Curio.Infrastructure.Services;

/// <summary>
/// 死信队列监控服务
/// 监控失败的事件并发送告警
/// </summary>
public class DeadLetterQueueMonitor : BackgroundService
{
    private readonly IGrainFactory _grainFactory;
    private readonly ILogger<DeadLetterQueueMonitor> _logger;
    private readonly ResilientPublishOptions _options;
    private readonly IServiceProvider _serviceProvider;

    public DeadLetterQueueMonitor(
        IGrainFactory grainFactory,
        ILogger<DeadLetterQueueMonitor> logger,
        IOptions<ResilientPublishOptions> options,
        IServiceProvider serviceProvider)
    {
        _grainFactory = grainFactory;
        _logger = logger;
        _options = options.Value;
        _serviceProvider = serviceProvider;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Dead Letter Queue Monitor started");

        try
        {
            // 等待Orleans客户端连接
            await WaitForOrleansConnectionAsync(stoppingToken);

            // 订阅死信队列
            await SubscribeToDeadLetterQueueAsync(stoppingToken);
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Dead Letter Queue Monitor stopped");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Dead Letter Queue Monitor failed");
        }
    }

    private async Task WaitForOrleansConnectionAsync(CancellationToken stoppingToken)
    {
        var retryCount = 0;
        const int maxRetries = 30; // 30次重试，每次等待2秒

        while (!stoppingToken.IsCancellationRequested && retryCount < maxRetries)
        {
            try
            {
                // 通过专用的监控Grain来访问Stream Provider
                var monitorGrain = _grainFactory.GetGrain<IDeadLetterMonitorGrain>("DeadLetterMonitor");
                await monitorGrain.InitializeAsync();
                
                _logger.LogInformation("Dead letter monitoring grain initialized successfully");
                return;
            }
            catch (Exception ex)
            {
                retryCount++;
                _logger.LogWarning("Failed to initialize dead letter monitoring (attempt {Attempt}/{MaxRetries}): {Error}",
                    retryCount, maxRetries, ex.Message);

                if (retryCount >= maxRetries)
                {
                    throw new InvalidOperationException("Failed to initialize dead letter monitoring after maximum retries", ex);
                }

                await Task.Delay(TimeSpan.FromSeconds(2), stoppingToken);
            }
        }
        
        if (stoppingToken.IsCancellationRequested)
        {
            throw new OperationCanceledException("Service was cancelled during initialization");
        }
    }

    private async Task SubscribeToDeadLetterQueueAsync(CancellationToken stoppingToken)
    {
        var monitorGrain = _grainFactory.GetGrain<IDeadLetterMonitorGrain>("DeadLetterMonitor");
        
        // 启动监控Grain的订阅
        await monitorGrain.StartMonitoringAsync();
        
        _logger.LogInformation("Successfully started dead letter queue monitoring via grain");

        // 保持服务运行
        while (!stoppingToken.IsCancellationRequested)
        {
            await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            
            // 可以定期检查监控状态
            var status = await monitorGrain.GetMonitoringStatusAsync();
            if (!status.IsActive)
            {
                _logger.LogWarning("Dead letter monitoring is not active, attempting to restart");
                await monitorGrain.StartMonitoringAsync();
            }
        }
    }

    private async Task ProcessDeadLetterEventAsync(DeadLetterEvent deadLetterEvent)
    {
        _logger.LogError("Dead letter event detected - EventId: {OriginalEventId}, Type: {EventType}, GrainId: {GrainId}, Reason: {Reason}, RetryCount: {RetryCount}",
            deadLetterEvent.OriginalEventId,
            deadLetterEvent.OriginalEventType,
            deadLetterEvent.GrainId,
            deadLetterEvent.FailureReason,
            deadLetterEvent.RetryCount);

        // 发送告警通知
        await SendAlertAsync(deadLetterEvent);

        // 记录到监控系统
        await RecordMetricsAsync(deadLetterEvent);

        // 可选：尝试恢复事件（如果配置了自动恢复）
        if (ShouldAttemptRecovery(deadLetterEvent))
        {
            await AttemptEventRecoveryAsync(deadLetterEvent);
        }
    }

    private async Task SendAlertAsync(DeadLetterEvent deadLetterEvent)
    {
        try
        {
            // 这里实现告警逻辑
            // 可以发送到邮件、Slack、钉钉、企业微信等
            
            var alertMessage = $"""
                🚨 Dead Letter Event Alert
                
                Event ID: {deadLetterEvent.OriginalEventId}
                Event Type: {deadLetterEvent.OriginalEventType}
                Grain ID: {deadLetterEvent.GrainId}
                Failure Reason: {deadLetterEvent.FailureReason}
                Retry Count: {deadLetterEvent.RetryCount}
                Failure Time: {deadLetterEvent.FailureTimestamp:yyyy-MM-dd HH:mm:ss} UTC
                
                Please check the system and take appropriate action.
                """;

            _logger.LogWarning("Alert sent for dead letter event: {AlertMessage}", alertMessage);

            // TODO: 集成实际的告警系统
            // await _notificationService.SendAlertAsync(alertMessage);
            
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send alert for dead letter event: {EventId}", deadLetterEvent.OriginalEventId);
        }
    }

    private async Task RecordMetricsAsync(DeadLetterEvent deadLetterEvent)
    {
        try
        {
            // 记录指标到监控系统
            // TODO: 集成Prometheus、Application Insights等监控系统
            
            _logger.LogInformation("Dead letter metrics recorded - EventType: {EventType}, GrainId: {GrainId}",
                deadLetterEvent.OriginalEventType, deadLetterEvent.GrainId);
                
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record metrics for dead letter event: {EventId}", deadLetterEvent.OriginalEventId);
        }
    }

    private bool ShouldAttemptRecovery(DeadLetterEvent deadLetterEvent)
    {
        // 定义自动恢复的条件
        // 例如：特定类型的事件、失败次数较少等
        
        var recoverableEventTypes = new[] { "UserRegisteredEvent", "UserLoginAttemptedEvent" };
        var isRecoverableType = recoverableEventTypes.Contains(deadLetterEvent.OriginalEventType);
        var isRecentFailure = DateTime.UtcNow - deadLetterEvent.FailureTimestamp < TimeSpan.FromHours(1);
        
        return isRecoverableType && isRecentFailure && deadLetterEvent.RetryCount <= 3;
    }

    private async Task AttemptEventRecoveryAsync(DeadLetterEvent deadLetterEvent)
    {
        try
        {
            _logger.LogInformation("Attempting to recover dead letter event: {EventId}, Type: {EventType}",
                deadLetterEvent.OriginalEventId, deadLetterEvent.OriginalEventType);

            // TODO: 实现事件恢复逻辑
            // 1. 解析原始事件数据
            // 2. 重新创建事件对象
            // 3. 重新发布到正常的事件流
            
            // 示例代码：
            // var originalEvent = JsonSerializer.Deserialize<DomainEvent>(deadLetterEvent.OriginalEventData);
            // await _resilientEventPublisher.PublishAsync(originalEvent, deadLetterEvent.GrainId);
            
            _logger.LogInformation("Successfully recovered dead letter event: {EventId}", deadLetterEvent.OriginalEventId);
            
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to recover dead letter event: {EventId}", deadLetterEvent.OriginalEventId);
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Stopping Dead Letter Queue Monitor");
        await base.StopAsync(cancellationToken);
    }
}