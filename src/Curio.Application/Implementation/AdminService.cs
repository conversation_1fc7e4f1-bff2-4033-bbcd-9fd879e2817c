using Orleans;
using Microsoft.Extensions.Logging;
using Curio.Application.Interfaces;
using Curio.Orleans.Interfaces.Admins;
using Curio.Shared.Admins;
using Curio.Shared.Users;

namespace Curio.Application.Implementation;

public class AdminService : IAdminService
{
    private readonly IGrainFactory _grainFactory;
    private readonly ILogger<AdminService> _logger;

    public AdminService(IGrainFactory grainFactory, ILogger<AdminService> logger)
    {
        _grainFactory = grainFactory;
        _logger = logger;
    }

    // 管理员管理
    public async Task<AdminDto?> GetAdminAsync(string adminId)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(adminId);
            return await adminGrain.GetAdminAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin: {AdminId}", adminId);
            return null;
        }
    }

    public async Task<AdminOperationResult> CreateAdminAsync(CreateAdminCommand command)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.Username);
            return await adminGrain.CreateAdminAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating admin: {Username}", command.Username);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to create admin",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<AdminOperationResult> UpdateAdminAsync(UpdateAdminCommand command)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
            return await adminGrain.UpdateAdminAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating admin: {AdminId}", command.AdminId);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to update admin",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<AdminOperationResult> ChangePasswordAsync(ChangeAdminPasswordCommand command)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
            return await adminGrain.ChangePasswordAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing password: {AdminId}", command.AdminId);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to change password",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<LoginResult> LoginAsync(AdminLoginCommand command)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.Username);
            var result = await adminGrain.LoginAsync(command);
            
            // 如果登录成功，生成JWT Token（这里需要集成JWT服务）
            if (result.Success && result.Admin != null)
            {
                // TODO: 生成JWT Token
                result.AccessToken = GenerateAccessToken(result.Admin);
                result.RefreshToken = GenerateRefreshToken(result.Admin.Id);
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login: {Username}", command.Username);
            return new LoginResult
            {
                Success = false,
                Message = "Login failed due to internal error"
            };
        }
    }

    public async Task<AdminOperationResult> AssignRoleAsync(AssignRoleCommand command)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
            return await adminGrain.AssignRoleAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning role: {AdminId}", command.AdminId);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to assign role",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<AdminOperationResult> RemoveRoleAsync(RemoveRoleCommand command)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
            return await adminGrain.RemoveRoleAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing role: {AdminId}", command.AdminId);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to remove role",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<AdminOperationResult> UpdateStatusAsync(UpdateAdminStatusCommand command)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
            return await adminGrain.UpdateStatusAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating admin status: {AdminId}", command.AdminId);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to update admin status",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<AdminOperationResult> ResetPasswordAsync(string adminId, string newPassword, string operatorId)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(adminId);
            return await adminGrain.ResetPasswordAsync(newPassword, operatorId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting password: {AdminId}", adminId);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to reset password",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<AdminOperationResult> UnlockAccountAsync(string adminId, string operatorId)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(adminId);
            return await adminGrain.UnlockAccountAsync(operatorId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unlocking account: {AdminId}", adminId);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to unlock account",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    // 2FA管理
    public async Task<TwoFactorSetupResult> EnableTwoFactorAsync(EnableTwoFactorCommand command)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
            return await adminGrain.EnableTwoFactorAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling 2FA: {AdminId}", command.AdminId);
            return new TwoFactorSetupResult
            {
                Success = false,
                Message = "Failed to enable two-factor authentication"
            };
        }
    }

    public async Task<AdminOperationResult> VerifyTwoFactorAsync(VerifyTwoFactorCommand command)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
            return await adminGrain.VerifyTwoFactorAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying 2FA: {AdminId}", command.AdminId);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to verify two-factor code",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<AdminOperationResult> DisableTwoFactorAsync(DisableTwoFactorCommand command)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
            return await adminGrain.DisableTwoFactorAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disabling 2FA: {AdminId}", command.AdminId);
            return new AdminOperationResult
            {
                Success = false,
                Message = "Failed to disable two-factor authentication",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<RecoveryCodesResult> RegenerateRecoveryCodesAsync(RegenerateRecoveryCodesCommand command)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
            return await adminGrain.RegenerateRecoveryCodesAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error regenerating recovery codes: {AdminId}", command.AdminId);
            return new RecoveryCodesResult
            {
                Success = false,
                Message = "Failed to regenerate recovery codes"
            };
        }
    }

    public async Task<int> GetRemainingRecoveryCodesCountAsync(string adminId)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(adminId);
            return await adminGrain.GetRemainingRecoveryCodesCountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting remaining recovery codes count: {AdminId}", adminId);
            return 0;
        }
    }

    // 权限检查
    public async Task<PermissionCheckResult> HasPermissionAsync(CheckPermissionCommand command)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(command.AdminId);
            return await adminGrain.HasPermissionAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission: {AdminId}", command.AdminId);
            return new PermissionCheckResult
            {
                HasPermission = false,
                AdminId = command.AdminId,
                Resource = command.Resource,
                Action = command.Action
            };
        }
    }

    public async Task<List<PermissionDto>> GetAdminPermissionsAsync(string adminId)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(adminId);
            return await adminGrain.GetPermissionsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin permissions: {AdminId}", adminId);
            return new List<PermissionDto>();
        }
    }

    public async Task<List<RoleDto>> GetAdminRolesAsync(string adminId)
    {
        try
        {
            var adminGrain = _grainFactory.GetGrain<IAdminGrain>(adminId);
            return await adminGrain.GetRolesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin roles: {AdminId}", adminId);
            return new List<RoleDto>();
        }
    }

    // 查询功能
    public async Task<PagedResult<AdminSummaryDto>> SearchAdminsAsync(AdminSearchRequest request)
    {
        try
        {
            // TODO: 实现管理员搜索功能
            // 这里需要使用查询投影或直接查询数据库
            await Task.CompletedTask;
            
            return new PagedResult<AdminSummaryDto>
            {
                Items = new List<AdminSummaryDto>(),
                TotalCount = 0,
                PageSize = request.Take,
                Page = request.Skip / request.Take + 1,
                TotalPages = 0,
                HasNext = false,
                HasPrevious = false
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching admins");
            return new PagedResult<AdminSummaryDto>();
        }
    }

    public async Task<AdminStatsDto> GetAdminStatsAsync()
    {
        try
        {
            // TODO: 实现管理员统计功能
            await Task.CompletedTask;
            
            return new AdminStatsDto
            {
                TotalAdmins = 0,
                ActiveAdmins = 0,
                LockedAdmins = 0,
                AdminsWithTwoFactor = 0,
                PendingSetupAdmins = 0,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin stats");
            return new AdminStatsDto { LastUpdated = DateTime.UtcNow };
        }
    }

    // 角色管理
    public async Task<RoleDto?> GetRoleAsync(string roleId)
    {
        try
        {
            var roleGrain = _grainFactory.GetGrain<IRoleGrain>(roleId);
            return await roleGrain.GetRoleAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role: {RoleId}", roleId);
            return null;
        }
    }

    public async Task<RoleOperationResult> CreateRoleAsync(CreateRoleCommand command)
    {
        try
        {
            var roleGrain = _grainFactory.GetGrain<IRoleGrain>(command.RoleName);
            return await roleGrain.CreateRoleAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating role: {RoleName}", command.RoleName);
            return new RoleOperationResult
            {
                Success = false,
                Message = "Failed to create role",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<RoleOperationResult> UpdateRoleAsync(UpdateRoleCommand command)
    {
        try
        {
            var roleGrain = _grainFactory.GetGrain<IRoleGrain>(command.RoleId);
            return await roleGrain.UpdateRoleAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating role: {RoleId}", command.RoleId);
            return new RoleOperationResult
            {
                Success = false,
                Message = "Failed to update role",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<RoleOperationResult> DeleteRoleAsync(DeleteRoleCommand command)
    {
        try
        {
            var roleGrain = _grainFactory.GetGrain<IRoleGrain>(command.RoleId);
            return await roleGrain.DeleteRoleAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting role: {RoleId}", command.RoleId);
            return new RoleOperationResult
            {
                Success = false,
                Message = "Failed to delete role",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<RoleOperationResult> AssignPermissionAsync(AssignPermissionCommand command)
    {
        try
        {
            var roleGrain = _grainFactory.GetGrain<IRoleGrain>(command.RoleId);
            return await roleGrain.AssignPermissionAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning permission to role: {RoleId}", command.RoleId);
            return new RoleOperationResult
            {
                Success = false,
                Message = "Failed to assign permission",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<RoleOperationResult> RemovePermissionAsync(RemovePermissionCommand command)
    {
        try
        {
            var roleGrain = _grainFactory.GetGrain<IRoleGrain>(command.RoleId);
            return await roleGrain.RemovePermissionAsync(command);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing permission from role: {RoleId}", command.RoleId);
            return new RoleOperationResult
            {
                Success = false,
                Message = "Failed to remove permission",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<RoleOperationResult> AssignPermissionsAsync(string roleId, List<PermissionAssignment> permissions, string assignedBy)
    {
        try
        {
            var roleGrain = _grainFactory.GetGrain<IRoleGrain>(roleId);
            return await roleGrain.AssignPermissionsAsync(permissions, assignedBy);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch assigning permissions to role: {RoleId}", roleId);
            return new RoleOperationResult
            {
                Success = false,
                Message = "Failed to assign permissions",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<RoleOperationResult> RemovePermissionsAsync(string roleId, List<PermissionAssignment> permissions, string removedBy)
    {
        try
        {
            var roleGrain = _grainFactory.GetGrain<IRoleGrain>(roleId);
            return await roleGrain.RemovePermissionsAsync(permissions, removedBy);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error batch removing permissions from role: {RoleId}", roleId);
            return new RoleOperationResult
            {
                Success = false,
                Message = "Failed to remove permissions",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<List<PermissionDto>> GetRolePermissionsAsync(string roleId)
    {
        try
        {
            var roleGrain = _grainFactory.GetGrain<IRoleGrain>(roleId);
            return await roleGrain.GetPermissionsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role permissions: {RoleId}", roleId);
            return new List<PermissionDto>();
        }
    }

    public async Task<PagedResult<RoleSummaryDto>> SearchRolesAsync(string? keyword = null, bool? isActive = null, int skip = 0, int take = 20)
    {
        try
        {
            // TODO: 实现角色搜索功能
            await Task.CompletedTask;
            
            return new PagedResult<RoleSummaryDto>
            {
                Items = new List<RoleSummaryDto>(),
                TotalCount = 0,
                PageSize = take,
                Page = skip / take + 1,
                TotalPages = 0,
                HasNext = false,
                HasPrevious = false
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching roles");
            return new PagedResult<RoleSummaryDto>();
        }
    }

    public async Task<RoleStatsDto> GetRoleStatsAsync()
    {
        try
        {
            // TODO: 实现角色统计功能
            await Task.CompletedTask;
            
            return new RoleStatsDto
            {
                TotalRoles = 0,
                ActiveRoles = 0,
                BuiltInRoles = 0,
                CustomRoles = 0,
                LastUpdated = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting role stats");
            return new RoleStatsDto { LastUpdated = DateTime.UtcNow };
        }
    }

    // 权限元数据
    public Task<List<PermissionGroupDto>> GetAllPermissionsAsync()
    {
        return GetPermissionGroupsAsync();
    }

    public Task<List<PermissionGroupDto>> GetPermissionGroupsAsync()
    {
        var permissionGroups = new List<PermissionGroupDto>();
        
        // 获取所有权限资源
        var resources = Enum.GetValues<PermissionResource>();
        
        foreach (var resource in resources)
        {
            var group = new PermissionGroupDto
            {
                Resource = resource,
                ResourceName = GetResourceDisplayName(resource),
                Description = GetResourceDescription(resource),
                Actions = new List<PermissionActionDto>()
            };
            
            // 获取所有权限操作
            var actions = Enum.GetValues<PermissionAction>();
            foreach (var action in actions)
            {
                group.Actions.Add(new PermissionActionDto
                {
                    Action = action,
                    ActionName = GetActionDisplayName(action),
                    Description = GetActionDescription(action),
                    IsGranted = false // 默认未授权
                });
            }
            
            permissionGroups.Add(group);
        }
        
        return Task.FromResult(permissionGroups);
    }

    // 私有辅助方法
    private string GenerateAccessToken(AdminDto admin)
    {
        // TODO: 实现JWT Token生成
        return $"access_token_{admin.Id}_{DateTime.UtcNow.Ticks}";
    }

    private string GenerateRefreshToken(string adminId)
    {
        // TODO: 实现JWT Refresh Token生成
        return $"refresh_token_{adminId}_{DateTime.UtcNow.Ticks}";
    }

    private static string GetResourceDisplayName(PermissionResource resource)
    {
        return resource switch
        {
            PermissionResource.Users => "用户管理",
            PermissionResource.Admins => "管理员管理",
            PermissionResource.Roles => "角色管理",
            PermissionResource.Permissions => "权限管理",
            PermissionResource.SystemConfig => "系统配置",
            PermissionResource.AuditLogs => "审计日志",
            PermissionResource.EmailTemplates => "邮件模板",
            PermissionResource.Monitoring => "系统监控",
            PermissionResource.ProjectionManagement => "投影管理",
            PermissionResource.DeadLetterQueue => "死信队列管理",
            _ => resource.ToString()
        };
    }

    private static string GetResourceDescription(PermissionResource resource)
    {
        return resource switch
        {
            PermissionResource.Users => "管理系统用户，包括注册、登录、状态等",
            PermissionResource.Admins => "管理系统管理员账户",
            PermissionResource.Roles => "管理角色定义和分配",
            PermissionResource.Permissions => "管理权限定义和分配",
            PermissionResource.SystemConfig => "管理系统配置参数",
            PermissionResource.AuditLogs => "查看和管理审计日志",
            PermissionResource.EmailTemplates => "管理邮件模板",
            PermissionResource.Monitoring => "系统监控和健康检查",
            PermissionResource.ProjectionManagement => "管理事件投影和重建",
            PermissionResource.DeadLetterQueue => "管理死信队列和失败事件",
            _ => $"{resource}相关功能"
        };
    }

    private static string GetActionDisplayName(PermissionAction action)
    {
        return action switch
        {
            PermissionAction.Read => "查看",
            PermissionAction.Create => "创建",
            PermissionAction.Update => "更新",
            PermissionAction.Delete => "删除",
            PermissionAction.Execute => "执行",
            PermissionAction.Manage => "管理",
            _ => action.ToString()
        };
    }

    private static string GetActionDescription(PermissionAction action)
    {
        return action switch
        {
            PermissionAction.Read => "查看和读取数据",
            PermissionAction.Create => "创建新数据",
            PermissionAction.Update => "更新现有数据",
            PermissionAction.Delete => "删除数据",
            PermissionAction.Execute => "执行操作或命令",
            PermissionAction.Manage => "完全管理权限",
            _ => $"执行{action}操作"
        };
    }
}