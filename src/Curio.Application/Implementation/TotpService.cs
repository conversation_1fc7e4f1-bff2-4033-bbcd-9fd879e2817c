using System.Security.Cryptography;
using System.Text;
using Curio.Application.Interfaces;

namespace Curio.Application.Implementation;

/// <summary>
/// TOTP (Time-based One-Time Password) 服务实现
/// 基于 RFC 6238 标准
/// </summary>
public class TotpService : ITotpService
{
    private const int SecretKeyLength = 20; // 160 bits
    private const int CodeLength = 6;
    private const int TimeStep = 30; // 30秒时间窗口
    private const long UnixEpoch = 621355968000000000L; // .NET DateTime ticks for Unix epoch

    public string GenerateSecret()
    {
        var key = new byte[SecretKeyLength];
        using (var rng = RandomNumberGenerator.Create())
        {
            rng.GetBytes(key);
        }
        return Base32Encode(key);
    }

    public string GenerateCode(string secret)
    {
        var secretBytes = Base32Decode(secret);
        var timeCounter = GetTimeCounter(DateTime.UtcNow);
        return GenerateCodeFromCounter(secretBytes, timeCounter);
    }

    public bool VerifyCode(string code, string secret, int toleranceSteps = 1)
    {
        if (string.IsNullOrEmpty(code) || string.IsNullOrEmpty(secret))
            return false;

        if (code.Length != CodeLength || !code.All(char.IsDigit))
            return false;

        try
        {
            var secretBytes = Base32Decode(secret);
            var currentTimeCounter = GetTimeCounter(DateTime.UtcNow);

            // 检查当前时间窗口以及前后容差范围内的时间窗口
            for (int i = -toleranceSteps; i <= toleranceSteps; i++)
            {
                var timeCounter = currentTimeCounter + i;
                var expectedCode = GenerateCodeFromCounter(secretBytes, timeCounter);
                
                if (code == expectedCode)
                    return true;
            }

            return false;
        }
        catch
        {
            return false;
        }
    }

    public string GenerateQrCodeUrl(string accountName, string secret, string issuer = "CurioAdmin")
    {
        var encodedIssuer = Uri.EscapeDataString(issuer);
        var encodedAccountName = Uri.EscapeDataString($"{issuer}:{accountName}");
        var encodedSecret = Uri.EscapeDataString(secret);
        
        return $"otpauth://totp/{encodedAccountName}?secret={encodedSecret}&issuer={encodedIssuer}&algorithm=SHA1&digits={CodeLength}&period={TimeStep}";
    }

    public List<string> GenerateRecoveryCodes(int count = 10)
    {
        var codes = new List<string>();
        using (var rng = RandomNumberGenerator.Create())
        {
            for (int i = 0; i < count; i++)
            {
                var codeBytes = new byte[5]; // 40 bits = 10 hex characters
                rng.GetBytes(codeBytes);
                
                // 格式化为 XXXXX-XXXXX 形式
                var hexString = Convert.ToHexString(codeBytes).ToLower();
                var formattedCode = $"{hexString.Substring(0, 5)}-{hexString.Substring(5, 5)}";
                codes.Add(formattedCode);
            }
        }
        return codes;
    }

    public bool VerifyRecoveryCode(string code, List<string> validCodes)
    {
        if (string.IsNullOrEmpty(code) || validCodes == null || !validCodes.Any())
            return false;

        // 标准化输入代码格式
        var normalizedInputCode = code.Replace("-", "").Replace(" ", "").ToLower();
        
        foreach (var validCode in validCodes)
        {
            var normalizedValidCode = validCode.Replace("-", "").Replace(" ", "").ToLower();
            if (normalizedInputCode == normalizedValidCode)
                return true;
        }

        return false;
    }

    // 私有辅助方法

    private static long GetTimeCounter(DateTime dateTime)
    {
        var unixTime = (dateTime.Ticks - UnixEpoch) / TimeSpan.TicksPerSecond;
        return unixTime / TimeStep;
    }

    private static string GenerateCodeFromCounter(byte[] secret, long timeCounter)
    {
        var counterBytes = BitConverter.GetBytes(timeCounter);
        if (BitConverter.IsLittleEndian)
        {
            Array.Reverse(counterBytes);
        }

        using (var hmac = new HMACSHA1(secret))
        {
            var hash = hmac.ComputeHash(counterBytes);
            var offset = hash[hash.Length - 1] & 0xF;
            
            var binaryCode = ((hash[offset] & 0x7F) << 24) |
                           ((hash[offset + 1] & 0xFF) << 16) |
                           ((hash[offset + 2] & 0xFF) << 8) |
                           (hash[offset + 3] & 0xFF);

            var code = binaryCode % (int)Math.Pow(10, CodeLength);
            return code.ToString().PadLeft(CodeLength, '0');
        }
    }

    private static string Base32Encode(byte[] data)
    {
        const string base32Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
        var result = new StringBuilder();
        
        for (int i = 0; i < data.Length; i += 5)
        {
            var chunk = new byte[5];
            var chunkLength = Math.Min(5, data.Length - i);
            Array.Copy(data, i, chunk, 0, chunkLength);
            
            var b = ((long)chunk[0] << 32) |
                   ((long)(chunk[1] & 0xFF) << 24) |
                   ((long)(chunk[2] & 0xFF) << 16) |
                   ((long)(chunk[3] & 0xFF) << 8) |
                   (chunk[4] & 0xFF);

            for (int j = 7; j >= 0; j--)
            {
                if (i * 8 / 5 + (7 - j) < data.Length * 8 / 5.0)
                {
                    result.Append(base32Chars[(int)((b >> (j * 5)) & 0x1F)]);
                }
            }
        }

        // 添加填充
        while (result.Length % 8 != 0)
        {
            result.Append('=');
        }

        return result.ToString();
    }

    private static byte[] Base32Decode(string base32)
    {
        const string base32Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
        
        // 移除填充和空格
        base32 = base32.Replace("=", "").Replace(" ", "").ToUpper();
        
        var result = new List<byte>();
        var buffer = 0L;
        var bitsInBuffer = 0;

        foreach (char c in base32)
        {
            var value = base32Chars.IndexOf(c);
            if (value < 0)
                throw new ArgumentException($"Invalid Base32 character: {c}");

            buffer = (buffer << 5) | value;
            bitsInBuffer += 5;

            if (bitsInBuffer >= 8)
            {
                result.Add((byte)(buffer >> (bitsInBuffer - 8)));
                bitsInBuffer -= 8;
            }
        }

        return result.ToArray();
    }
}