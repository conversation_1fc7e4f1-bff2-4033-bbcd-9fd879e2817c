using Curio.Shared.Admins;

namespace Curio.Application.Interfaces;

public interface IAdminService
{
    // 管理员管理
    Task<AdminDto?> GetAdminAsync(string adminId);
    Task<AdminOperationResult> CreateAdminAsync(CreateAdminCommand command);
    Task<AdminOperationResult> UpdateAdminAsync(UpdateAdminCommand command);
    Task<AdminOperationResult> ChangePasswordAsync(ChangeAdminPasswordCommand command);
    Task<LoginResult> LoginAsync(AdminLoginCommand command);
    Task<AdminOperationResult> AssignRoleAsync(AssignRoleCommand command);
    Task<AdminOperationResult> RemoveRoleAsync(RemoveRoleCommand command);
    Task<AdminOperationResult> UpdateStatusAsync(UpdateAdminStatusCommand command);
    Task<AdminOperationResult> ResetPasswordAsync(string adminId, string newPassword, string operatorId);
    Task<AdminOperationResult> UnlockAccountAsync(string adminId, string operatorId);
    
    // 2FA管理
    Task<TwoFactorSetupResult> EnableTwoFactorAsync(EnableTwoFactorCommand command);
    Task<AdminOperationResult> DisableTwoFactorAsync(DisableTwoFactorCommand command);
    Task<AdminOperationResult> VerifyTwoFactorAsync(VerifyTwoFactorCommand command);
    Task<RecoveryCodesResult> RegenerateRecoveryCodesAsync(RegenerateRecoveryCodesCommand command);
    Task<int> GetRemainingRecoveryCodesCountAsync(string adminId);
    
    // 权限检查
    Task<PermissionCheckResult> HasPermissionAsync(CheckPermissionCommand command);
    Task<List<PermissionDto>> GetAdminPermissionsAsync(string adminId);
    Task<List<RoleDto>> GetAdminRolesAsync(string adminId);
    
    // 查询功能
    Task<PagedResult<AdminSummaryDto>> SearchAdminsAsync(AdminSearchRequest request);
    Task<AdminStatsDto> GetAdminStatsAsync();
    
    // 角色管理
    Task<RoleDto?> GetRoleAsync(string roleId);
    Task<RoleOperationResult> CreateRoleAsync(CreateRoleCommand command);
    Task<RoleOperationResult> UpdateRoleAsync(UpdateRoleCommand command);
    Task<RoleOperationResult> DeleteRoleAsync(DeleteRoleCommand command);
    Task<RoleOperationResult> AssignPermissionAsync(AssignPermissionCommand command);
    Task<RoleOperationResult> RemovePermissionAsync(RemovePermissionCommand command);
    Task<RoleOperationResult> AssignPermissionsAsync(string roleId, List<PermissionAssignment> permissions, string assignedBy);
    Task<RoleOperationResult> RemovePermissionsAsync(string roleId, List<PermissionAssignment> permissions, string removedBy);
    Task<List<PermissionDto>> GetRolePermissionsAsync(string roleId);
    Task<PagedResult<RoleSummaryDto>> SearchRolesAsync(string? keyword = null, bool? isActive = null, int skip = 0, int take = 20);
    Task<RoleStatsDto> GetRoleStatsAsync();
    
    // 权限元数据
    Task<List<PermissionGroupDto>> GetAllPermissionsAsync();
    Task<List<PermissionGroupDto>> GetPermissionGroupsAsync();
}