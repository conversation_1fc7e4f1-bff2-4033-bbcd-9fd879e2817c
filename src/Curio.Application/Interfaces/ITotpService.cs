namespace Curio.Application.Interfaces;

/// <summary>
/// TOTP (Time-based One-Time Password) 服务接口
/// </summary>
public interface ITotpService
{
    /// <summary>
    /// 生成密钥
    /// </summary>
    string GenerateSecret();
    
    /// <summary>
    /// 生成当前时间的TOTP代码
    /// </summary>
    string GenerateCode(string secret);
    
    /// <summary>
    /// 验证TOTP代码
    /// </summary>
    /// <param name="code">用户输入的6位数字代码</param>
    /// <param name="secret">密钥</param>
    /// <param name="toleranceSteps">允许的时间步数容差（默认1，允许前后30秒）</param>
    bool VerifyCode(string code, string secret, int toleranceSteps = 1);
    
    /// <summary>
    /// 生成QR码URL
    /// </summary>
    /// <param name="accountName">账户名称</param>
    /// <param name="secret">密钥</param>
    /// <param name="issuer">发行者名称</param>
    string GenerateQrCodeUrl(string accountName, string secret, string issuer = "CurioAdmin");
    
    /// <summary>
    /// 生成恢复代码
    /// </summary>
    /// <param name="count">生成的恢复代码数量</param>
    List<string> GenerateRecoveryCodes(int count = 10);
    
    /// <summary>
    /// 验证恢复代码
    /// </summary>
    /// <param name="code">恢复代码</param>
    /// <param name="validCodes">有效的恢复代码列表</param>
    bool VerifyRecoveryCode(string code, List<string> validCodes);
}