<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Orleans.Sdk" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Core.Abstractions" Version="9.2.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Curio.Events\Curio.Events.csproj" />
    <ProjectReference Include="..\Curio.Orleans.Interfaces\Curio.Orleans.Interfaces.csproj" />
    <ProjectReference Include="..\Curio.Shared\Curio.Shared.csproj" />
  </ItemGroup>

</Project>