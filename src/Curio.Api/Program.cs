using Microsoft.Extensions.Configuration;
using Orleans;
using Orleans.Configuration;
using Orleans.Hosting;
using Scalar.AspNetCore;
using Curio.Application.Interfaces;
using Curio.Application.Implementation;
using Curio.Commands.Handlers;
using Curio.Queries.Handlers;
using Curio.Projections.Handlers;
using Curio.Infrastructure;
using Curio.Infrastructure.Configuration;
using Curio.Orleans.Interfaces;

var builder = WebApplication.CreateBuilder(args);

// 配置灵活的配置加载（支持 appsettings.Local.json）
builder.Configuration.AddFlexibleConfiguration(builder.Environment);

// Add services to the container.
builder.Services.AddControllers();
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

// Get Orleans configuration
var orleansSettings = builder.Configuration.GetSection(OrleansSettings.SectionName).Get<OrleansSettings>() ?? new OrleansSettings();
var clusteringConnectionString = builder.Configuration.GetOrleansConnectionString("clustering");

// Configure Orleans Client
builder.Services.AddOrleansClient(clientBuilder =>
{
    clientBuilder
        .UseAdoNetClustering(options =>
        {
            options.ConnectionString = clusteringConnectionString;
            options.Invariant = "Npgsql";
        })
        .Configure<ClusterOptions>(options =>
        {
            options.ClusterId = orleansSettings.ClusterId;
            options.ServiceId = orleansSettings.ServiceId;
        });
});

// Register new CQRS handlers (主要架构)
builder.Services.AddScoped<UserCommandHandler>();
builder.Services.AddScoped<UserQueryHandler>();
builder.Services.AddScoped<EmailQueryHandler>();
builder.Services.AddScoped<ProjectionQueryHandler>();

// Register legacy services (向后兼容，逐步移除)
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IEmailStatusService, EmailStatusService>();
builder.Services.AddScoped<IAdminService, AdminService>();
builder.Services.AddInfrastructureServices(builder.Configuration);

// Add health checks
builder.Services.AddInfrastructureHealthChecks(builder.Configuration);

// Add background services
builder.Services.AddInfrastructureBackgroundServices();

var app = builder.Build();

// 开发环境下显示配置摘要
if (app.Environment.IsDevelopment())
{
    ConfigurationDebugHelper.PrintConfigurationSummary(app.Configuration, app.Environment);
    ConfigurationDebugHelper.ValidateKeyConfigurations(app.Configuration);
}

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.MapScalarApiReference();
}

app.UseHttpsRedirection();

app.UseAuthorization();

// Add health check endpoints
app.MapHealthChecks("/health");
app.MapHealthChecks("/health/ready", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
{
    Predicate = check => check.Tags.Contains("ready")
});
app.MapHealthChecks("/health/live", new Microsoft.AspNetCore.Diagnostics.HealthChecks.HealthCheckOptions
{
    Predicate = _ => false
});

app.MapControllers();

// 数据初始化已移至 Orleans Silo 项目中

app.Run();
