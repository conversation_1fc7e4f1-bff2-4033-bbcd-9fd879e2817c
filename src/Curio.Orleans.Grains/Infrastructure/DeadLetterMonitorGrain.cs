using Orleans;
using Orleans.Streams;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Curio.Orleans.Interfaces.Infrastructure;
using Curio.Shared.Users;

namespace Curio.Orleans.Grains.Infrastructure;

/// <summary>
/// 死信队列监控Grain实现
/// 在Orleans Grain上下文中处理Stream Provider访问
/// </summary>
public class DeadLetterMonitorGrain : Grain, IDeadLetterMonitorGrain
{
    private readonly ILogger<DeadLetterMonitorGrain> _logger;
    private IAsyncStream<DeadLetterEvent>? _deadLetterStream;
    private StreamSubscriptionHandle<DeadLetterEvent>? _subscriptionHandle;
    private MonitoringStatus _status;
    private long _processedEventCount;

    public DeadLetterMonitorGrain(ILogger<DeadLetterMonitorGrain> logger)
    {
        _logger = logger;
        _status = new MonitoringStatus
        {
            IsActive = false,
            LastEventTime = DateTime.UtcNow,
            ProcessedEventCount = 0
        };
    }

    public override async Task OnActivateAsync(CancellationToken cancellationToken)
    {
        await base.OnActivateAsync(cancellationToken);
        _logger.LogInformation("DeadLetterMonitorGrain activated: {GrainId}", this.GetPrimaryKeyString());
    }

    public Task InitializeAsync()
    {
        try
        {
            // 在Grain上下文中初始化Stream Provider
            var streamProvider = this.GetStreamProvider("KafkaStreams");
            _deadLetterStream = streamProvider.GetStream<DeadLetterEvent>("dead-letter-queue", "monitor");
            
            _logger.LogInformation("Dead letter stream initialized successfully in grain context");
            return Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize dead letter stream in grain context");
            _status = _status with { LastError = ex.Message };
            throw;
        }
    }

    public async Task StartMonitoringAsync()
    {
        if (_deadLetterStream == null)
        {
            throw new InvalidOperationException("Dead letter stream is not initialized. Call InitializeAsync first.");
        }

        if (_subscriptionHandle != null)
        {
            _logger.LogInformation("Dead letter monitoring is already active");
            return;
        }

        try
        {
            _subscriptionHandle = await _deadLetterStream.SubscribeAsync(async (deadLetterEvent, token) =>
            {
                try
                {
                    await ProcessDeadLetterEventAsync(deadLetterEvent);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process dead letter event: {EventId}", deadLetterEvent.OriginalEventId);
                    _status = _status with { LastError = ex.Message };
                }
            });

            _status = _status with 
            { 
                IsActive = true, 
                LastError = null 
            };

            _logger.LogInformation("Successfully started dead letter queue monitoring");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to start dead letter monitoring");
            _status = _status with 
            { 
                IsActive = false, 
                LastError = ex.Message 
            };
            throw;
        }
    }

    public async Task StopMonitoringAsync()
    {
        if (_subscriptionHandle != null)
        {
            try
            {
                await _subscriptionHandle.UnsubscribeAsync();
                _subscriptionHandle = null;
                
                _status = _status with { IsActive = false };
                _logger.LogInformation("Stopped dead letter queue monitoring");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to stop dead letter monitoring");
                throw;
            }
        }
    }

    public Task<MonitoringStatus> GetMonitoringStatusAsync()
    {
        return Task.FromResult(_status with { ProcessedEventCount = _processedEventCount });
    }

    private async Task ProcessDeadLetterEventAsync(DeadLetterEvent deadLetterEvent)
    {
        _processedEventCount++;
        _status = _status with { LastEventTime = DateTime.UtcNow };

        _logger.LogError("Dead letter event detected - EventId: {OriginalEventId}, Type: {EventType}, GrainId: {GrainId}, Reason: {Reason}, RetryCount: {RetryCount}",
            deadLetterEvent.OriginalEventId,
            deadLetterEvent.OriginalEventType,
            deadLetterEvent.GrainId,
            deadLetterEvent.FailureReason,
            deadLetterEvent.RetryCount);

        // 发送告警通知
        await SendAlertAsync(deadLetterEvent);

        // 记录到监控系统
        await RecordMetricsAsync(deadLetterEvent);

        // 可选：尝试恢复事件（如果配置了自动恢复）
        if (ShouldAttemptRecovery(deadLetterEvent))
        {
            await AttemptEventRecoveryAsync(deadLetterEvent);
        }
    }

    private async Task SendAlertAsync(DeadLetterEvent deadLetterEvent)
    {
        try
        {
            // 这里实现告警逻辑
            // 可以发送到邮件、Slack、钉钉、企业微信等
            
            var alertMessage = $"""
                🚨 Dead Letter Event Alert
                
                Event ID: {deadLetterEvent.OriginalEventId}
                Event Type: {deadLetterEvent.OriginalEventType}
                Grain ID: {deadLetterEvent.GrainId}
                Failure Reason: {deadLetterEvent.FailureReason}
                Retry Count: {deadLetterEvent.RetryCount}
                Failure Time: {deadLetterEvent.FailureTimestamp:yyyy-MM-dd HH:mm:ss} UTC
                
                Please check the system and take appropriate action.
                """;

            _logger.LogWarning("Alert sent for dead letter event: {AlertMessage}", alertMessage);

            // TODO: 集成实际的告警系统
            // await _notificationService.SendAlertAsync(alertMessage);
            
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send alert for dead letter event: {EventId}", deadLetterEvent.OriginalEventId);
        }
    }

    private async Task RecordMetricsAsync(DeadLetterEvent deadLetterEvent)
    {
        try
        {
            // 记录指标到监控系统
            // TODO: 集成Prometheus、Application Insights等监控系统
            
            _logger.LogInformation("Dead letter metrics recorded - EventType: {EventType}, GrainId: {GrainId}",
                deadLetterEvent.OriginalEventType, deadLetterEvent.GrainId);
                
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record metrics for dead letter event: {EventId}", deadLetterEvent.OriginalEventId);
        }
    }

    private bool ShouldAttemptRecovery(DeadLetterEvent deadLetterEvent)
    {
        // 定义自动恢复的条件
        // 例如：特定类型的事件、失败次数较少等
        
        var recoverableEventTypes = new[] { "UserRegisteredEvent", "UserLoginAttemptedEvent" };
        var isRecoverableType = recoverableEventTypes.Contains(deadLetterEvent.OriginalEventType);
        var isRecentFailure = DateTime.UtcNow - deadLetterEvent.FailureTimestamp < TimeSpan.FromHours(1);
        
        return isRecoverableType && isRecentFailure && deadLetterEvent.RetryCount <= 3;
    }

    private async Task AttemptEventRecoveryAsync(DeadLetterEvent deadLetterEvent)
    {
        try
        {
            _logger.LogInformation("Attempting to recover dead letter event: {EventId}, Type: {EventType}",
                deadLetterEvent.OriginalEventId, deadLetterEvent.OriginalEventType);

            // TODO: 实现事件恢复逻辑
            // 1. 解析原始事件数据
            // 2. 重新创建事件对象
            // 3. 重新发布到正常的事件流
            
            // 示例代码：
            // var originalEvent = JsonSerializer.Deserialize<DomainEvent>(deadLetterEvent.OriginalEventData);
            // await _resilientEventPublisher.PublishAsync(originalEvent, deadLetterEvent.GrainId);
            
            _logger.LogInformation("Successfully recovered dead letter event: {EventId}", deadLetterEvent.OriginalEventId);
            
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to recover dead letter event: {EventId}", deadLetterEvent.OriginalEventId);
        }
    }

    public override async Task OnDeactivateAsync(DeactivationReason reason, CancellationToken cancellationToken)
    {
        await StopMonitoringAsync();
        await base.OnDeactivateAsync(reason, cancellationToken);
        
        _logger.LogInformation("DeadLetterMonitorGrain deactivated: {GrainId}, Reason: {Reason}", 
            this.GetPrimaryKeyString(), reason);
    }
}